import React from 'react';
import { ContentPillar } from '@/types/pillar';

interface SimplifiedScheduleFlowProps {
  pillars: ContentPillar[];
  heatmapData: any;
}

const SimplifiedScheduleFlow: React.FC<SimplifiedScheduleFlowProps> = ({ pillars, heatmapData }) => {
  // Component implementation
  return (
    <div className="p-4 bg-gray-800 rounded-lg">
      <h3 className="text-lg font-medium text-white mb-4">Your Optimal Publishing Schedule</h3>
      {/* Schedule content */}
    </div>
  );
};

export default SimplifiedScheduleFlow;
