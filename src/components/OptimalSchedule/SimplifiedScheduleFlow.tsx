import React from 'react';
import { ContentPillar } from '@/types/pillar';
import { Clock, Calendar, TrendingUp, Copy } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SimplifiedScheduleFlowProps {
  pillars: ContentPillar[];
  heatmapData: any;
}

const SimplifiedScheduleFlow: React.FC<SimplifiedScheduleFlowProps> = ({ pillars }) => {
  // Check if we have real pillar data
  const hasRealData = pillars && pillars.length > 0;

  // Generate schedule based on actual user pillars
  const generateUserSchedule = () => {
    if (!hasRealData) return [];

    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    // Use optimal times based on general best practices
    const times = ['12:00 PM', '1:00 PM', '3:00 PM', '5:00 PM', '2:00 PM', '4:00 PM', '6:00 PM'];

    return pillars.slice(0, 7).map((pillar, index) => ({
      day: days[index] || days[index % 7],
      pillar: pillar.name,
      time: times[index] || '2:00 PM',
      boost: pillar.video_count > 0 ? Math.min(Math.round((pillar.video_count / 10) * 15), 25) : 5,
      color: pillar.color
    }));
  };

  const schedule = generateUserSchedule();

  const copySchedule = () => {
    if (schedule.length === 0) return;

    const scheduleText = schedule.map(item =>
      `${item.day}: ${item.pillar} at ${item.time} (+${item.boost}% expected boost)`
    ).join('\n');

    navigator.clipboard.writeText(scheduleText);
  };

  if (!hasRealData) {
    return (
      <div className="bg-gray-800/50 border border-gray-600/50 rounded-lg p-6">
        <div className="flex items-center gap-3 mb-4">
          <Calendar className="w-5 h-5 text-teal-400" />
          <h3 className="text-lg font-medium text-white">Your Optimal Publishing Schedule</h3>
        </div>

        <div className="text-center py-8">
          <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-300 mb-2">No Schedule Available</h4>
          <p className="text-gray-400 text-sm">
            Create content pillars and publish videos to get personalized scheduling recommendations.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800/50 border border-gray-600/50 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Calendar className="w-5 h-5 text-teal-400" />
          <h3 className="text-lg font-medium text-white">Your Optimal Publishing Schedule</h3>
        </div>

        <Button
          onClick={copySchedule}
          variant="outline"
          size="sm"
          className="flex items-center gap-2 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
        >
          <Copy className="w-4 h-4" />
          Copy Schedule
        </Button>
      </div>

      <div className="space-y-3">
        {schedule.map((item, index) => (
          <div key={index} className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30">
            <div className="flex items-center space-x-3">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: item.color }}
              />
              <div>
                <div className="font-medium text-white">{item.day}</div>
                <div className="text-sm text-gray-300">{item.pillar}</div>
              </div>
            </div>

            <div className="text-right">
              <div className="font-medium text-white">{item.time}</div>
              <div className="text-sm text-teal-400 flex items-center gap-1">
                <TrendingUp className="w-3 h-3" />
                +{item.boost}% boost
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <p className="text-sm text-blue-300">
          💡 This schedule is based on your content pillars and general best practices.
          As you publish more content, we'll refine these recommendations with your actual performance data.
        </p>
      </div>
    </div>
  );
};

export default SimplifiedScheduleFlow;
