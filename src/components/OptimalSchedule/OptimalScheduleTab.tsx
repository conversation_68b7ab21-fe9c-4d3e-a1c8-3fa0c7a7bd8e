
import { useState } from 'react';
import ScheduleHeatmap from './ScheduleHeatmap';
import SimplifiedScheduleFlow from './SimplifiedScheduleFlow';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  actual_percentage: number;
  video_count: number;
  avg_views: number;
  best_day: string;
  best_day_boost: number;
}

interface OptimalScheduleTabProps {
  pillars: ContentPillar[];
}

const OptimalScheduleTab = ({ pillars }: OptimalScheduleTabProps) => {
  const [heatmapData, setHeatmapData] = useState(null);

  // Add a callback function to receive heatmap data
  const onHeatmapDataReady = (data) => {
    setHeatmapData(data);
  };

  return (
    <div className="space-y-8">
      {/* Simplified Header */}
      <div className="text-center space-y-2 mb-6">
        <h2 className="text-2xl font-bold text-white">Your Optimal Publishing Schedule</h2>
        <p className="text-gray-300">Based on your historical performance data from the last 90 days</p>
      </div>

      {/* Only show the simplified schedule flow */}
      <SimplifiedScheduleFlow pillars={pillars} heatmapData={heatmapData} />

      {/* Hidden heatmap component to process data */}
      <div className="hidden">
        <ScheduleHeatmap onDataReady={onHeatmapDataReady} />
      </div>
    </div>
  );
};

export default OptimalScheduleTab;
