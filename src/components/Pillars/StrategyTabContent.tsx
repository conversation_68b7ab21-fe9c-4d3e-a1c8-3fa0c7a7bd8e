
import React, { useState } from 'react';
import PillarsGrid from '@/components/Pillars/PillarsGrid';
import NextPillarFocus from '@/components/Pillars/NextPillarFocus';
import CompactContentBalance from '@/components/Pillars/CompactContentBalance';
import PillarAnalyticsModal from '@/components/Pillars/PillarAnalyticsModal';
import PillarModal from '@/components/Pillars/PillarModal';
import ErrorBoundary from '@/components/ui/error-boundary';
import { ContentPillar } from '@/types/pillar';
import SimplifiedScheduleFlow from '@/components/OptimalSchedule/SimplifiedScheduleFlow';
import ScheduleHeatmap from '@/components/OptimalSchedule/ScheduleHeatmap';
import { TrendingUp, Target, Lightbulb } from 'lucide-react';

interface StrategyTabContentProps {
  pillars: ContentPillar[];
  onDeletePillar: (pillarId: string, pillarName: string) => void;
  onEditPillar: (pillar: ContentPillar) => void;
  onViewVideos?: (pillarId: string) => void;
  addPillar: (pillar: any) => Promise<boolean>;
  canAddMorePillars: boolean;
  userTier: string;
  pillarLimit: number;
}

const StrategyTabContent = ({ 
  pillars, 
  onDeletePillar, 
  onEditPillar, 
  onViewVideos,
  addPillar,
  canAddMorePillars,
  userTier,
  pillarLimit
}: StrategyTabContentProps) => {
  const [analyticsModalPillar, setAnalyticsModalPillar] = useState<ContentPillar | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [heatmapData, setHeatmapData] = useState(null);

  const handleViewAnalytics = (pillar: ContentPillar) => {
    setAnalyticsModalPillar(pillar);
  };

  const handleCloseAnalytics = () => {
    setAnalyticsModalPillar(null);
  };

  const handleAddPillar = () => {
    setIsAddModalOpen(true);
  };

  const handleAddPillarSubmit = async (pillarData: any) => {
    const success = await addPillar(pillarData);
    if (success) {
      setIsAddModalOpen(false);
    }
    return success;
  };

  const onHeatmapDataReady = (data: any) => {
    setHeatmapData(data);
  };

  // Generate strategy recommendations based on pillar data
  const getStrategyRecommendations = () => {
    if (!pillars || pillars.length === 0) return [];
    
    const recommendations = [];
    
    // Find pillar with lowest content
    const sortedPillars = [...pillars].sort((a, b) => 
      ((a.video_count || 0) - (b.video_count || 0))
    );
    
    if (sortedPillars.length > 0) {
      const lowestContentPillar = sortedPillars[0];
      if (lowestContentPillar && (lowestContentPillar.video_count || 0) < 3) {
        recommendations.push({
          icon: <TrendingUp className="w-4 h-4 text-teal" />,
          title: `Increase ${lowestContentPillar.name} Content`,
          description: `This pillar has fewer videos than your other topics. Consider creating more content to build audience in this area.`
        });
      }
    }
    
    // Add general recommendation
    recommendations.push({
      icon: <Target className="w-4 h-4 text-teal" />,
      title: "Follow Your Optimal Schedule",
      description: "Publishing according to your optimal schedule below can increase views by up to 23% based on your historical data."
    });
    
    // Add recommendation if we have enough pillars
    if (pillars.length >= 3) {
      recommendations.push({
        icon: <Lightbulb className="w-4 h-4 text-teal" />,
        title: "Maintain Content Balance",
        description: "Your audience responds best when you maintain a consistent mix of your content pillars."
      });
    }
    
    return recommendations;
  };

  const recommendations = getStrategyRecommendations();

  return (
    <>
      <div className="space-y-8">
        {/* Strategy Insights Section - Only show if pillars exist */}
        {pillars.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 h-full">
              <ErrorBoundary fallback={
                <div className="text-center py-8 h-full flex items-center justify-center">
                  <p className="text-gray-400">Unable to load pillar focus suggestions.</p>
                </div>
              }>
                <div className="h-full">
                  <NextPillarFocus pillars={pillars} />
                </div>
              </ErrorBoundary>
            </div>
            <div className="lg:col-span-1 h-full">
              <ErrorBoundary fallback={
                <div className="text-center py-8 h-full flex items-center justify-center">
                  <p className="text-gray-400">Unable to load content balance.</p>
                </div>
              }>
                <div className="h-full">
                  <CompactContentBalance pillars={pillars.map(pillar => ({
                    ...pillar,
                    target_percentage: pillar.target_percentage || 0,
                    actual_percentage: pillar.actual_percentage || 0
                  }))} />
                </div>
              </ErrorBoundary>
            </div>
          </div>
        )}

        {/* Optimal Schedule Section - Only show if pillars exist */}
        {pillars.length > 0 && (
          <ErrorBoundary fallback={
            <div className="text-center py-8">
              <p className="text-gray-400">Unable to load schedule recommendations.</p>
            </div>
          }>
            <SimplifiedScheduleFlow pillars={pillars} heatmapData={heatmapData} />
            <div className="hidden">
              <ScheduleHeatmap onDataReady={onHeatmapDataReady} />
            </div>
          </ErrorBoundary>
        )}

        {/* Pillars Grid */}
        <ErrorBoundary fallback={
          <div className="text-center py-8">
            <p className="text-gray-400">Unable to load pillars. Please refresh the page.</p>
          </div>
        }>
          <PillarsGrid 
            pillars={pillars} 
            onDeletePillar={onDeletePillar}
            onEditPillar={onEditPillar}
            onViewVideos={onViewVideos}
            onViewAnalytics={handleViewAnalytics}
            canAddMore={canAddMorePillars}
            onAddNew={handleAddPillar}
            userTier={userTier}
            pillarLimit={pillarLimit}
          />
        </ErrorBoundary>
      </div>

      {/* Analytics Modal */}
      <PillarAnalyticsModal
        pillar={analyticsModalPillar}
        isOpen={!!analyticsModalPillar}
        onClose={handleCloseAnalytics}
      />

      {/* Add Pillar Modal */}
      <PillarModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddPillarSubmit}
        title="Create New Content Pillar"
        submitLabel="Create Pillar"
        mode="add"
      />
    </>
  );
};

export default StrategyTabContent;
