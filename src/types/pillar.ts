
export interface ContentPillar {
  id: string;
  name: string;
  description: string;
  color: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
  video_count?: number;
  actual_percentage?: number;
  target_percentage?: number;
  best_day?: string;
  best_day_boost?: number;
}

export interface PillarData {
  id: string;
  name: string;
  description: string;
  color: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
  video_count?: number;
  actual_percentage: number;
  target_percentage: number;
  best_day?: string;
  best_day_boost?: number;
}
